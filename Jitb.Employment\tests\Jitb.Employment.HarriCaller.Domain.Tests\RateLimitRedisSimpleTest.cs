using System;
using System.Threading.Tasks;
using FluentAssertions;
using Jitb.Employment.HarriCaller.Domain.Providers.RateLimiting;
using NLog;
using StackExchange.Redis;
using Xunit;
using Xunit.Abstractions;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    public class RateLimitRedisSimpleTest : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly ILogger _logger;
        private readonly string _redisConnectionString;

        public RateLimitRedisSimpleTest(ITestOutputHelper output)
        {
            _output = output;
            _logger = LogManager.GetCurrentClassLogger();
            _redisConnectionString = "localhost:6379"; // Use local Redis for testing
            _output.WriteLine($"Using Redis connection: {_redisConnectionString}");
        }

        [Fact]
        public async Task RecordRequestAsync_ShouldWriteToRedis_LocalTest()
        {
            // Skip test if Redis is not available locally
            if (!await IsRedisAvailable())
            {
                _output.WriteLine("Redis is not available locally. Skipping test.");
                return;
            }

            RateLimitRedisFixed rateLimiter = null;
            try
            {
                rateLimiter = new RateLimitRedisFixed(_redisConnectionString, _logger, 3, "test-simple:");
                var key = $"test-redis-verify-{Guid.NewGuid()}";
                var fullKey = $"test-simple:{key}";

                // Record a request
                await rateLimiter.RecordRequestAsync(key);

                // Connect directly to Redis and verify the key exists and has at least one entry
                using (var redis = await ConnectionMultiplexer.ConnectAsync(_redisConnectionString))
                {
                    var db = redis.GetDatabase();
                    var length = await db.SortedSetLengthAsync(fullKey);
                    _output.WriteLine($"Redis sorted set '{fullKey}' length: {length}");
                    length.Should().BeGreaterThan(0, $"Sorted set '{fullKey}' should have at least one entry after RecordRequestAsync");

                    // Clean up the test key
                    await db.KeyDeleteAsync(fullKey);
                    _output.WriteLine($"Cleaned up test key: {fullKey}");
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Test failed with exception: {ex.Message}");
                throw;
            }
            finally
            {
                rateLimiter?.Dispose();
            }
        }

        [Fact]
        public async Task RecordRequestAsync_ShouldCreateUniqueEntries()
        {
            // Skip test if Redis is not available locally
            if (!await IsRedisAvailable())
            {
                _output.WriteLine("Redis is not available locally. Skipping test.");
                return;
            }

            RateLimitRedisFixed rateLimiter = null;
            try
            {
                rateLimiter = new RateLimitRedisFixed(_redisConnectionString, _logger, 10, "test-unique:");
                var key = $"test-unique-{Guid.NewGuid()}";
                var fullKey = $"test-unique:{key}";

                // Record multiple requests
                await rateLimiter.RecordRequestAsync(key);
                await rateLimiter.RecordRequestAsync(key);
                await rateLimiter.RecordRequestAsync(key);

                // Connect directly to Redis and verify we have 3 entries
                using (var redis = await ConnectionMultiplexer.ConnectAsync(_redisConnectionString))
                {
                    var db = redis.GetDatabase();
                    var length = await db.SortedSetLengthAsync(fullKey);
                    _output.WriteLine($"Redis sorted set '{fullKey}' length: {length}");
                    length.Should().Be(3, $"Sorted set '{fullKey}' should have exactly 3 entries after 3 RecordRequestAsync calls");

                    // Get all entries to verify they are unique
                    var entries = await db.SortedSetRangeByScoreWithScoresAsync(fullKey);
                    _output.WriteLine($"Entries in sorted set:");
                    foreach (var entry in entries)
                    {
                        _output.WriteLine($"  Member: {entry.Element}, Score: {entry.Score}");
                    }

                    entries.Length.Should().Be(3, "Should have 3 unique entries");

                    // Clean up the test key
                    await db.KeyDeleteAsync(fullKey);
                    _output.WriteLine($"Cleaned up test key: {fullKey}");
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Test failed with exception: {ex.Message}");
                throw;
            }
            finally
            {
                rateLimiter?.Dispose();
            }
        }

        private async Task<bool> IsRedisAvailable()
        {
            try
            {
                var options = ConfigurationOptions.Parse(_redisConnectionString);
                options.ConnectTimeout = 1000; // 1 second timeout
                options.SyncTimeout = 1000;
                options.AsyncTimeout = 1000;
                options.AbortOnConnectFail = true;

                using (var redis = await ConnectionMultiplexer.ConnectAsync(options))
                {
                    var db = redis.GetDatabase();
                    await db.PingAsync();
                    return true;
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Redis not available: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
