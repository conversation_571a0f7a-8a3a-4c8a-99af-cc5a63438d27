namespace Jitb.Employment.Domain.Repositories.Employment.Maps
{
    using Concepts;
    using FluentNHibernate.Mapping;
    using NHibernate.Type;

    public class EmployeeAuditMap :
        ClassMap<EmployeeAudit>
    {
        public EmployeeAuditMap()
        {

            Id(x => x.AuditID, "AuditID")
                .GeneratedBy
                .Identity()
                .Default(0);
            Map(x => x.AuditActionCode, "AuditActionCode")
                .CustomSqlType("varchar(30)");
            Map(x => x.AuditSource, "AuditSource")
                .CustomSqlType("varchar(30)");
            Map(x => x.AuditDateTimeStamp, "AuditDateTimeStamp")
                .CustomSqlType("datetime");
            Map(x => x.EmployeeID, "EmployeeID");
            Map(x => x.BadgeID, "BadgeID");
            Map(x => x.Address1, "Address1")
                .CustomSqlType("varchar(50)");
            Map(x => x.Address2, "Address2")
                .CustomSqlType("varchar(50)");
            Map(x => x.City, "City")
                .CustomSqlType("varchar(50)");
            Map(x => x.StateCode, "StateCode")
                .CustomSqlType("char(2)");
            Map(x => x.ZipCode, "ZipCode")
                .CustomSqlType("char(10)");
            Map(x => x.WorkPhoneNumber, "WorkPhoneNumber")
                .CustomSqlType("varchar(15)");
            Map(x => x.HomePhoneNumber, "HomePhoneNumber")
                .CustomSqlType("varchar(15)");
            Map(x => x.CellPhoneNumber, "CellPhoneNumber")
                .CustomSqlType("varchar(15)");
            Map(x => x.NetworkID, "NetworkID")
                .CustomSqlType("varchar(50)");
            Map(x => x.FimEmailPreferenceCode, "FimEmailPreferenceCode")
                .CustomSqlType("varchar(10)");
            Map(x => x.LawsonCompanyID, "LawsonCompanyID");
            Map(x => x.LawsonEmployeeID, "LawsonEmployeeID");
            Map(x => x.LawsonSupervisorCode, "LawsonSupervisorCode")
                .CustomSqlType("char(10)");
            Map(x => x.LawsonSupervisorID, "LawsonSupervisorID");
            Map(x => x.LawsonSuperKeyID, "LawsonSuperKeyID");
            Map(x => x.LawsonEmpStatus, "LawsonEmpStatus")
                .CustomSqlType("varchar(5)");
            Map(x => x.LawsonDepartmentCode, "LawsonDepartmentCode")
                .CustomSqlType("varchar(10)");
            Map(x => x.LawsonProcessLevelCode, "LawsonProcessLevelCode")
                .CustomSqlType("varchar(5)");
            Map(x => x.LawsonCostCenter, "LawsonCostCenter")
                .CustomSqlType("varchar(10)");
            Map(x => x.eRestaurantEmployeeID, "eRestaurantEmployeeID")
                .CustomSqlType("varchar(30)");
            Map(x => x.eRestaurantSupervisorID, "eRestaurantSupervisorID")
                .CustomSqlType("varchar(30)");
            Map(x => x.HREmployeeIdentityId, "HREmployeeIdentityID")
                .CustomSqlType("varchar(15)");
            Map(x => x.HREmployeeID, "HREmployeeID")
                .CustomSqlType("numeric(9,0)");
            Map(x => x.HRSupervisorID, "HRSupervisorID")
                .CustomSqlType("numeric(9,0)");
            Map(x => x.HRDepartmentCode, "HRDepartmentCode")
                .CustomSqlType("varchar(50)");
            Map(x => x.HRDepartment, "HRDepartment")
                .CustomSqlType("varchar(50)");
            Map(x => x.HRCompanyBrand, "HRCompanyBrand")
                .CustomSqlType("varchar(30)");
            Map(x => x.CostCenterCode, "CostCenterCode")
                .CustomSqlType("char(10)");
            Map(x => x.IsEligibleForRehire, "EligibleForRehire").CustomType<YesNoType>();
            Map(x => x.SupervisorEmployeeID, "SupervisorEmployeeID");
            Map(x => x.HireDate, "HireDate")
                .CustomSqlType("date");
            Map(x => x.PreviousHireDate, "PreviousHireDate")
                .CustomSqlType("date");
            Map(x => x.TerminationDate, "TerminationDate")
                .CustomSqlType("date");
            Map(x => x.AdjustedHireDate, "AdjustedHireDate")
                .CustomSqlType("date");
            Map(x => x.EmailAddress, "EmailAddress")
                .CustomSqlType("varchar(50)");
            Map(x => x.ExemptStatus, "ExemptStatus")
                .CustomSqlType("char(2)");
            Map(x => x.PayGrade, "PayGrade")
                .CustomSqlType("char(3)");
            Map(x => x.SalaryClass, "SalaryClass")
                .CustomSqlType("char(1)");
            Map(x => x.BirthDate, "BirthDate")
                .CustomSqlType("date");
            Map(x => x.Ssn, "Ssn")
                .CustomSqlType("varchar(11)");
            Map(x => x.Sex, "Sex")
                .CustomSqlType("char(1)");
            Map(x => x.CurrentStatus, "CurrentStatus")
                .CustomSqlType("char(2)");
            Map(x => x.EmployeeType, "EmployeeType")
                .CustomSqlType("char(1)");
            Map(x => x.MiddleInitial, "MiddleInitial")
                .CustomSqlType("char(1)");
            Map(x => x.FirstName, "FirstName")
                .CustomSqlType("varchar(50)");
            Map(x => x.MiddleName, "MiddleName")
                .CustomSqlType("varchar(50)");
            Map(x => x.LastName, "LastName")
                .CustomSqlType("varchar(50)");
            Map(x => x.NickName, "NickName")
                .CustomSqlType("varchar(50)");
            Map(x => x.DateStamp, "DateStamp");
            Map(x => x.IsTraining, "TrainingFlag").CustomType<YesNoType>();
            Map(x => x.DomainName, "DomainName")
                .CustomSqlType("char(10)");
            Map(x => x.JobCode, "JobCode")
                .CustomSqlType("varchar(10)");
            Map(x => x.JobClass, "JobClass")
                .CustomSqlType("varchar(10)");
            Map(x => x.JobCodeEffectiveDate, "JobCodeEffectiveDate");
            Map(x => x.DateTimeStamp, "DateTimeStamp");
            Map(x => x.MessageId, "MessageID")
                .CustomSqlType("nvarchar(255)");


        }
    }

}
