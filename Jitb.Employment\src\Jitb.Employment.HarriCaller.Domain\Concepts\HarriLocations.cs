using JetBrains.Annotations;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Jitb.Employment.HarriCaller.Domain.Concepts
{
    public class HarriLocations2List : List<HarriLocation2>
    {
    }

    public class HarriLocation2
    {
        [JsonProperty("id")][CanBeNull] public int Id { get; set; }
        [JsonProperty("name")][CanBeNull] public string Name { get; set; }
        [JsonProperty("type")][CanBeNull] public string Type { get; set; }
        [JsonProperty("state_code")][CanBeNull] public string StateCode { get; set; }
        [JsonProperty("city")][CanBeNull] public string City { get; set; }
    }
}
