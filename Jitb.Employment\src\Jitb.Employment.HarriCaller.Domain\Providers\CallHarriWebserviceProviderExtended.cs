﻿using Jitb.Employment.Domain.Dictionaries;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.Providers
{
    public interface ICallHarriWebserviceDeserialize<T> : ICallHarriWebServiceProvider
    {
        Task<T> Deserialize<T>(RestResponse response);
    }

    public class CallHarriWebserviceDeserialize<T> : CallHarriWebServiceProvider, ICallHarriWebserviceDeserialize<T>
    {
        public CallHarriWebserviceDeserialize(IHarriTenantTable harriTenantTable, ILogger log) : base(harriTenantTable, log)
        {
        }

        public async Task<T> Deserialize<T>(RestResponse response)
        {
            dynamic content = JsonConvert.DeserializeObject(response.Content);
            T result = JsonConvert.DeserializeObject<T>(content.ToString());
            return result;
        }
    }
}
